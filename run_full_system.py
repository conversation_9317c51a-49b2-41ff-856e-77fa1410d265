#!/usr/bin/env python3
"""
Full System Startup Script for AI Companion System
Runs both Gradio interface and FastAPI server simultaneously
"""

import asyncio
import subprocess
import sys
import time
import signal
import os
from pathlib import Path

def run_gradio():
    """Run the Gradio interface."""
    print("🌐 Starting Gradio interface...")
    env = os.environ.copy()
    env["GRADIO_PORT"] = "7861"
    return subprocess.Popen([
        sys.executable, "-m", "ai_companion.main"
    ], cwd=Path.cwd(), env=env)

def run_api():
    """Run the FastAPI server."""
    print("🚀 Starting FastAPI server...")
    return subprocess.Popen([
        "uvicorn", "ai_companion.interfaces.api:app",
        "--host", "0.0.0.0",
        "--port", "8001",
        "--reload"
    ], cwd=Path.cwd())

def main():
    """Main function to run both services."""
    print("🎯 Starting AI Companion System - Full Deployment")
    print("=" * 50)
    
    processes = []
    
    try:
        # Start Gradio interface
        gradio_process = run_gradio()
        processes.append(gradio_process)
        time.sleep(3)  # Give it time to start
        
        # Start API server
        api_process = run_api()
        processes.append(api_process)
        time.sleep(2)  # Give it time to start
        
        print("\n✅ Both services started successfully!")
        print("🌐 Gradio Interface: http://localhost:7861")
        print("🚀 API Documentation: http://localhost:8001/docs")
        print("❤️  Health Check: http://localhost:8001/health")
        print("\nPress Ctrl+C to stop all services...")
        
        # Wait for processes
        while True:
            time.sleep(1)
            # Check if any process has died
            for i, process in enumerate(processes):
                if process.poll() is not None:
                    print(f"⚠️  Process {i} has stopped unexpectedly")
                    return
                    
    except KeyboardInterrupt:
        print("\n🛑 Shutting down services...")
        
    finally:
        # Clean shutdown
        for process in processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
        
        print("✅ All services stopped")

if __name__ == "__main__":
    main()
