"""
Core Emotional Intelligence Service.
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from collections import defaultdict

from ..models import (
    EmotionType, InteractionType, MentalHealthRisk, TherapeuticTechnique,
    EmotionalState, utc_now
)
from ...services.gemini import GeminiService
from .patterns import EmotionPatternMatcher


class EmotionalIntelligenceService:
    """Advanced emotional intelligence service with AI and pattern-based analysis."""
    
    def __init__(self, gemini_service: GeminiService):
        """Initialize emotional intelligence service."""
        self.logger = logging.getLogger(__name__)
        self.gemini_service = gemini_service
        self.pattern_matcher = EmotionPatternMatcher()
        
        # User emotional history tracking
        self.user_emotional_history: Dict[str, List[EmotionalState]] = defaultdict(list)
        self.user_patterns: Dict[str, Dict[str, Any]] = defaultdict(dict)
        
        # Therapeutic response templates
        self.therapeutic_responses = self._initialize_therapeutic_responses()
        
        self.logger.info("✅ Emotional Intelligence Service initialized")
    
    def _initialize_therapeutic_responses(self) -> Dict[TherapeuticTechnique, List[str]]:
        """Initialize therapeutic response templates."""
        return {
            TherapeuticTechnique.VALIDATION: [
                "I hear that you're feeling {emotion}. That sounds really {intensity_word}.",
                "It makes sense that you would feel {emotion} given what you're going through.",
                "Your feelings are completely valid. It's understandable to feel {emotion}.",
                "Thank you for sharing how you're feeling. {emotion} is a natural response to what you're experiencing."
            ],
            TherapeuticTechnique.COGNITIVE_REFRAMING: [
                "I wonder if there might be another way to look at this situation?",
                "What would you tell a friend who was going through something similar?",
                "Sometimes our thoughts can be more harsh than reality. What evidence supports or challenges this thought?",
                "This feeling is temporary, even though it feels overwhelming right now."
            ],
            TherapeuticTechnique.MINDFULNESS: [
                "Let's take a moment to focus on your breathing. Can you take three deep breaths with me?",
                "Notice what you're feeling right now without trying to change it. What do you observe?",
                "Ground yourself by naming 5 things you can see, 4 things you can touch, 3 things you can hear.",
                "Your thoughts and feelings are like clouds passing through the sky - temporary and ever-changing."
            ],
            TherapeuticTechnique.BREATHING_EXERCISES: [
                "Try breathing in for 4 counts, holding for 4, and exhaling for 6. This can help calm your nervous system.",
                "Focus on making your exhale longer than your inhale. This activates your body's relaxation response.",
                "Place one hand on your chest and one on your belly. Try to breathe so only the bottom hand moves.",
                "Breathe in slowly through your nose, pause, then breathe out slowly through your mouth."
            ],
            TherapeuticTechnique.GROUNDING: [
                "Let's ground you in the present moment. What are 3 things you can see around you right now?",
                "Feel your feet on the ground. Notice the temperature of the air on your skin.",
                "Name the day, date, time, and where you are. This helps anchor you in the present.",
                "Hold something with texture - a stone, fabric, or ice cube. Focus on how it feels."
            ]
        }
    
    async def analyze_emotion(
        self,
        text: str,
        user_id: str = None,
        context: Dict[str, Any] = None
    ) -> EmotionalState:
        """Analyze emotional content with advanced AI and pattern recognition."""
        try:
            # Pattern-based emotion detection
            pattern_emotions = self.pattern_matcher.detect_emotions_by_pattern(text)
            
            # AI-powered emotional analysis
            ai_analysis = await self.gemini_service.analyze_emotional_content(text)
            
            # Combine analyses
            primary_emotion, intensity = self._combine_emotion_analyses(
                pattern_emotions, ai_analysis, text
            )
            
            # Detect secondary emotions
            secondary_emotions = self._detect_secondary_emotions(text, primary_emotion)
            
            # Assess mental health risk
            risk_level = self._assess_mental_health_risk(text, primary_emotion, intensity)
            
            # Identify context factors
            context_factors = self._identify_context_factors(text, context)
            
            # Create emotional state
            emotional_state = EmotionalState(
                primary_emotion=primary_emotion,
                intensity=intensity,
                secondary_emotions=secondary_emotions,
                context_factors=context_factors,
                risk_level=risk_level,
                timestamp=utc_now()
            )
            
            # Track user emotional history
            if user_id:
                self._update_user_emotional_history(user_id, emotional_state)
            
            return emotional_state
            
        except Exception as e:
            self.logger.error(f"Error analyzing emotion: {e}")
            # Return neutral emotional state on error
            return EmotionalState(
                primary_emotion=EmotionType.NEUTRAL,
                intensity=0.5,
                secondary_emotions={},
                context_factors=[],
                risk_level=MentalHealthRisk.LOW,
                timestamp=utc_now()
            )
    
    def _combine_emotion_analyses(
        self,
        pattern_emotions: Dict[EmotionType, float],
        ai_analysis: Optional[Dict[str, Any]],
        text: str
    ) -> tuple[EmotionType, float]:
        """Combine pattern-based and AI emotion analyses."""
        # Start with pattern-based emotions
        combined_scores = pattern_emotions.copy()
        
        # Add AI analysis if available
        if ai_analysis and 'emotions' in ai_analysis:
            ai_emotions = ai_analysis['emotions']
            for emotion_str, score in ai_emotions.items():
                try:
                    emotion = EmotionType(emotion_str.lower())
                    # Weight AI analysis slightly higher
                    combined_scores[emotion] = combined_scores.get(emotion, 0) + (score * 1.2)
                except ValueError:
                    continue
        
        # Determine primary emotion and intensity
        if combined_scores:
            primary_emotion = max(combined_scores.items(), key=lambda x: x[1])[0]
            intensity = min(combined_scores[primary_emotion], 1.0)
        else:
            # Fallback to neutral if no emotions detected
            primary_emotion = EmotionType.NEUTRAL
            intensity = 0.5
        
        return primary_emotion, intensity
    
    def _detect_secondary_emotions(
        self,
        text: str,
        primary_emotion: EmotionType
    ) -> Dict[EmotionType, float]:
        """Detect secondary emotions in the text."""
        pattern_emotions = self.pattern_matcher.detect_emotions_by_pattern(text)
        
        # Remove primary emotion and return others
        secondary = {
            emotion: score for emotion, score in pattern_emotions.items()
            if emotion != primary_emotion and score > 0.3
        }
        
        return secondary
    
    def _assess_mental_health_risk(
        self,
        text: str,
        primary_emotion: EmotionType,
        intensity: float
    ) -> MentalHealthRisk:
        """Assess mental health risk based on emotional analysis."""
        # Use pattern matcher for crisis risk assessment
        pattern_risk = self.pattern_matcher.assess_crisis_risk(text)
        
        # Adjust based on emotional intensity
        high_risk_emotions = {
            EmotionType.SADNESS, EmotionType.FEAR, EmotionType.ANXIETY,
            EmotionType.LONELINESS, EmotionType.SHAME, EmotionType.GUILT
        }
        
        if primary_emotion in high_risk_emotions and intensity > 0.8:
            if pattern_risk == MentalHealthRisk.LOW:
                return MentalHealthRisk.MODERATE
            elif pattern_risk == MentalHealthRisk.MODERATE:
                return MentalHealthRisk.HIGH
        
        return pattern_risk
    
    def _identify_context_factors(
        self,
        text: str,
        context: Optional[Dict[str, Any]]
    ) -> List[str]:
        """Identify contextual factors affecting emotional state."""
        factors = []
        
        # Time-based factors
        if context and 'time_of_day' in context:
            hour = context['time_of_day']
            if hour < 6 or hour > 22:
                factors.append("late_night_early_morning")
        
        # Content-based factors
        text_lower = text.lower()
        
        if any(word in text_lower for word in ['work', 'job', 'boss', 'colleague']):
            factors.append("work_related")
        
        if any(word in text_lower for word in ['family', 'parent', 'sibling', 'relative']):
            factors.append("family_related")
        
        if any(word in text_lower for word in ['relationship', 'partner', 'boyfriend', 'girlfriend']):
            factors.append("relationship_related")
        
        if any(word in text_lower for word in ['money', 'financial', 'bills', 'debt']):
            factors.append("financial_stress")
        
        if any(word in text_lower for word in ['health', 'sick', 'illness', 'pain']):
            factors.append("health_concerns")
        
        return factors
    
    def _update_user_emotional_history(self, user_id: str, emotional_state: EmotionalState):
        """Update user's emotional history and patterns."""
        # Add to history (keep last 100 entries)
        self.user_emotional_history[user_id].append(emotional_state)
        if len(self.user_emotional_history[user_id]) > 100:
            self.user_emotional_history[user_id] = self.user_emotional_history[user_id][-100:]
        
        # Update patterns
        self._update_user_patterns(user_id)
    
    def _update_user_patterns(self, user_id: str):
        """Update user emotional patterns."""
        history = self.user_emotional_history[user_id]
        if len(history) < 5:
            return
        
        # Calculate dominant emotions
        emotion_counts = defaultdict(int)
        total_intensity = 0
        risk_levels = []
        
        for state in history[-20:]:  # Last 20 interactions
            emotion_counts[state.primary_emotion] += 1
            total_intensity += state.intensity
            risk_levels.append(state.risk_level)
        
        # Update patterns
        self.user_patterns[user_id] = {
            'dominant_emotions': dict(emotion_counts),
            'average_intensity': total_intensity / len(history[-20:]),
            'risk_trend': self._calculate_risk_trend(risk_levels),
            'last_updated': utc_now()
        }
    
    def _calculate_risk_trend(self, risk_levels: List[MentalHealthRisk]) -> str:
        """Calculate trend in mental health risk."""
        if len(risk_levels) < 3:
            return "insufficient_data"
        
        # Convert to numeric values for trend calculation
        risk_values = [risk.value for risk in risk_levels]
        
        # Simple trend calculation
        recent_avg = sum(risk_values[-5:]) / min(len(risk_values), 5)
        older_avg = sum(risk_values[:-5]) / max(len(risk_values) - 5, 1)
        
        if recent_avg > older_avg + 0.5:
            return "increasing"
        elif recent_avg < older_avg - 0.5:
            return "decreasing"
        else:
            return "stable"
    
    async def generate_therapeutic_response(
        self,
        message: str,
        user_id: str,
        technique: TherapeuticTechnique,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate therapeutic response using specified technique."""
        try:
            # Get emotional analysis
            emotional_state = await self.analyze_emotion(message, user_id, context)
            
            # Get template response
            template_response = self._generate_template_response(
                technique, emotional_state.primary_emotion, emotional_state.intensity
            )
            
            # Use AI to enhance the response
            enhanced_response = await self.gemini_service.generate_therapeutic_response(
                message=message,
                technique=technique,
                emotional_context=emotional_state,
                template=template_response
            )
            
            return {
                "response": enhanced_response or template_response,
                "technique_used": technique.value,
                "emotional_state": emotional_state,
                "confidence": 0.8 if enhanced_response else 0.6
            }
            
        except Exception as e:
            self.logger.error(f"Error generating therapeutic response: {e}")
            return {
                "response": "I'm here to support you. How are you feeling right now?",
                "technique_used": technique.value,
                "error": str(e)
            }
    
    def _generate_template_response(
        self,
        technique: TherapeuticTechnique,
        emotion: EmotionType,
        intensity: float
    ) -> str:
        """Generate template-based therapeutic response."""
        templates = self.therapeutic_responses.get(technique, [
            "I'm here to support you through this."
        ])
        
        # Select template based on emotion and intensity
        template = templates[0] if templates else "I'm here to support you."
        
        # Fill in placeholders
        intensity_word = "intense" if intensity > 0.7 else "challenging" if intensity > 0.4 else "difficult"
        
        response = template.format(
            emotion=emotion.value,
            intensity_word=intensity_word
        )
        
        return response
    
    def get_user_emotional_summary(self, user_id: str) -> Dict[str, Any]:
        """Get emotional summary for a user."""
        history = self.user_emotional_history.get(user_id, [])
        patterns = self.user_patterns.get(user_id, {})
        
        if not history:
            return {"message": "No emotional history available"}
        
        return {
            "total_interactions": len(history),
            "recent_emotions": [state.primary_emotion.value for state in history[-5:]],
            "patterns": patterns,
            "current_risk_level": history[-1].risk_level.value if history else "unknown",
            "last_interaction": history[-1].timestamp if history else None
        }
