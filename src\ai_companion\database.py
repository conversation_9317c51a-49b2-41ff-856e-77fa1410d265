"""
Database module for the AI Companion System.
Provides centralized database connection pooling and session management.
"""

import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from .config.settings import settings

logger = logging.getLogger(__name__)

# Global database engines and session factories
engine = None
async_engine = None
SessionLocal = None
AsyncSessionLocal = None


def initialize_database():
    """Initialize database engines and session factories."""
    global engine, async_engine, SessionLocal, AsyncSessionLocal
    
    try:
        # Determine database URL and connection args
        if settings.database_url.startswith("sqlite"):
            # SQLite-specific configuration
            connect_args = {
                "check_same_thread": False,
                "timeout": 20
            }
            poolclass = StaticPool
        else:
            # PostgreSQL/MySQL configuration
            connect_args = {}
            poolclass = None
        
        # Create synchronous engine with appropriate pooling for database type
        if settings.database_url.startswith("sqlite"):
            # SQLite doesn't support traditional connection pooling
            engine = create_engine(
                settings.database_url,
                pool_pre_ping=True,
                connect_args=connect_args,
                poolclass=poolclass,
                echo=settings.debug_mode
            )
        else:
            # PostgreSQL/MySQL with full connection pooling
            engine = create_engine(
                settings.database_url,
                pool_pre_ping=True,
                pool_recycle=3600,  # Recycle connections every hour
                pool_size=5,        # Number of connections to maintain
                max_overflow=10,    # Additional connections when pool is full
                connect_args=connect_args,
                echo=settings.debug_mode
            )
        
        # Create asynchronous engine for async operations
        if settings.database_url.startswith("sqlite"):
            async_url = settings.database_url.replace("sqlite://", "sqlite+aiosqlite://")
        elif settings.database_url.startswith("postgresql"):
            async_url = settings.database_url.replace("postgresql://", "postgresql+asyncpg://")
        else:
            async_url = settings.database_url
            
        # Create async engine with appropriate settings
        if settings.database_url.startswith("sqlite"):
            async_engine = create_async_engine(
                async_url,
                pool_pre_ping=True,
                connect_args=connect_args,
                echo=settings.debug_mode
            )
        else:
            async_engine = create_async_engine(
                async_url,
                pool_pre_ping=True,
                pool_recycle=3600,
                pool_size=5,
                max_overflow=10,
                echo=settings.debug_mode
            )
        
        # Create session factories
        SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=engine
        )
        
        AsyncSessionLocal = async_sessionmaker(
            bind=async_engine,
            class_=AsyncSession,
            autocommit=False,
            autoflush=False,
            expire_on_commit=False
        )
        
        logger.info("✅ Database connection pooling initialized successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize database: {e}")
        raise


def get_db() -> Session:
    """
    Dependency for FastAPI that provides a SQLAlchemy session.
    Ensures the session is always closed after the request.
    """
    if SessionLocal is None:
        initialize_database()
    
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@asynccontextmanager
async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Async context manager that provides an async SQLAlchemy session.
    Ensures the session is always closed after use.
    """
    if AsyncSessionLocal is None:
        initialize_database()
    
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def close_database():
    """Close database connections and clean up resources."""
    global engine, async_engine
    
    try:
        if async_engine:
            await async_engine.dispose()
            logger.info("Async database engine disposed")
        
        if engine:
            engine.dispose()
            logger.info("Database engine disposed")
            
    except Exception as e:
        logger.error(f"Error closing database connections: {e}")


def get_database_health() -> dict:
    """Check database health and return status information."""
    try:
        if engine is None:
            return {"status": "not_initialized", "healthy": False}
        
        # Test connection
        from sqlalchemy import text
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        
        # Get pool status (if available)
        pool = engine.pool
        pool_status = {}

        try:
            # These methods may not be available for all pool types (e.g., StaticPool)
            if hasattr(pool, 'size'):
                pool_status["size"] = pool.size()
            if hasattr(pool, 'checkedin'):
                pool_status["checked_in"] = pool.checkedin()
            if hasattr(pool, 'checkedout'):
                pool_status["checked_out"] = pool.checkedout()
            if hasattr(pool, 'overflow'):
                pool_status["overflow"] = pool.overflow()
            if hasattr(pool, 'invalid'):
                pool_status["invalid"] = pool.invalid()

            pool_status["pool_type"] = type(pool).__name__
        except Exception as e:
            pool_status["error"] = f"Could not get pool status: {e}"
        
        return {
            "status": "healthy",
            "healthy": True,
            "pool_status": pool_status,
            "database_url": settings.database_url.split("@")[-1] if "@" in settings.database_url else settings.database_url
        }
        
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "status": "unhealthy",
            "healthy": False,
            "error": str(e)
        }


# Initialize database on module import
try:
    initialize_database()
except Exception as e:
    logger.warning(f"Database initialization failed on import: {e}")
    logger.info("Database will be initialized on first use")
