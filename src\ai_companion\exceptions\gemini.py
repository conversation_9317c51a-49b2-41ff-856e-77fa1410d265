"""
Gemini-specific exceptions and error handling for the AI Companion System.
"""

from typing import Optional, Dict, Any
from .base import AICompanionException


class GeminiServiceError(AICompanionException):
    """Base exception for Gemini service errors."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        quota_exceeded: bool = False,
        retry_after: Optional[int] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(message)
        self.error_code = error_code
        self.quota_exceeded = quota_exceeded
        self.retry_after = retry_after
        self.original_error = original_error


class GeminiQuotaExceededError(GeminiServiceError):
    """Raised when Gemini API quota is exceeded."""

    def __init__(self, message: str = "Gemini API quota exceeded", retry_after: Optional[int] = None, original_error: Optional[Exception] = None):
        super().__init__(
            message=message,
            error_code="QUOTA_EXCEEDED",
            quota_exceeded=True,
            retry_after=retry_after,
            original_error=original_error
        )


class GeminiAuthenticationError(GeminiServiceError):
    """Raised when Gemini API authentication fails."""

    def __init__(self, message: str = "Gemini API authentication failed", original_error: Optional[Exception] = None):
        super().__init__(
            message=message,
            error_code="AUTH_FAILED",
            original_error=original_error
        )


class GeminiTimeoutError(GeminiServiceError):
    """Raised when Gemini API request times out."""

    def __init__(self, message: str = "Gemini API request timed out", original_error: Optional[Exception] = None):
        super().__init__(
            message=message,
            error_code="TIMEOUT",
            original_error=original_error
        )


class GeminiContentBlockedError(GeminiServiceError):
    """Raised when Gemini blocks content due to safety settings."""

    def __init__(self, message: str = "Content was blocked by Gemini safety settings", original_error: Optional[Exception] = None):
        super().__init__(
            message=message,
            error_code="CONTENT_BLOCKED",
            original_error=original_error
        )


class GeminiRateLimitError(GeminiServiceError):
    """Raised when Gemini API rate limit is exceeded."""

    def __init__(self, message: str = "Gemini API rate limit exceeded", retry_after: Optional[int] = None, original_error: Optional[Exception] = None):
        super().__init__(
            message=message,
            error_code="RATE_LIMITED",
            retry_after=retry_after,
            original_error=original_error
        )


def classify_gemini_error(error: Exception) -> GeminiServiceError:
    """
    Classify a generic exception into a specific Gemini error type.
    
    Args:
        error: The original exception
        
    Returns:
        A specific GeminiServiceError subclass
    """
    error_str = str(error).lower()
    
    # Check for quota/billing issues
    if any(keyword in error_str for keyword in ['quota', 'billing', 'exceeded', '429']):
        return GeminiQuotaExceededError(
            message=f"API quota exceeded: {error}",
            original_error=error
        )
    
    # Check for authentication issues
    if any(keyword in error_str for keyword in ['authentication', 'api key', 'unauthorized', '401', '403']):
        return GeminiAuthenticationError(
            message=f"Authentication failed: {error}",
            original_error=error
        )
    
    # Check for timeout issues
    if any(keyword in error_str for keyword in ['timeout', 'timed out', 'deadline']):
        return GeminiTimeoutError(
            message=f"Request timed out: {error}",
            original_error=error
        )
    
    # Check for rate limiting
    if any(keyword in error_str for keyword in ['rate limit', 'too many requests']):
        return GeminiRateLimitError(
            message=f"Rate limit exceeded: {error}",
            original_error=error
        )
    
    # Check for content blocking
    if any(keyword in error_str for keyword in ['blocked', 'safety', 'harmful']):
        return GeminiContentBlockedError(
            message=f"Content blocked: {error}",
            original_error=error
        )
    
    # Default to generic Gemini error
    return GeminiServiceError(
        message=f"Gemini service error: {error}",
        error_code="UNKNOWN",
        original_error=error
    )


def get_fallback_strategy(error: GeminiServiceError) -> Dict[str, Any]:
    """
    Get the appropriate fallback strategy for a Gemini error.
    
    Args:
        error: The Gemini error
        
    Returns:
        Dictionary with fallback strategy information
    """
    if isinstance(error, GeminiQuotaExceededError):
        return {
            "strategy": "wait_and_retry",
            "wait_time": error.retry_after or 3600,  # 1 hour default
            "use_fallback_response": True,
            "notify_admin": True
        }
    
    elif isinstance(error, GeminiAuthenticationError):
        return {
            "strategy": "fail_fast",
            "use_fallback_response": True,
            "notify_admin": True,
            "requires_manual_intervention": True
        }
    
    elif isinstance(error, GeminiTimeoutError):
        return {
            "strategy": "retry_with_backoff",
            "max_retries": 3,
            "backoff_factor": 2,
            "use_fallback_response": True
        }
    
    elif isinstance(error, GeminiRateLimitError):
        return {
            "strategy": "exponential_backoff",
            "wait_time": error.retry_after or 60,
            "max_retries": 5,
            "use_fallback_response": True
        }
    
    elif isinstance(error, GeminiContentBlockedError):
        return {
            "strategy": "use_fallback_immediately",
            "use_fallback_response": True,
            "log_for_review": True
        }
    
    else:
        return {
            "strategy": "retry_once_then_fallback",
            "max_retries": 1,
            "use_fallback_response": True
        }
