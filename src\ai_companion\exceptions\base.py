"""
Base exceptions for the AI Companion System.
"""

from typing import Op<PERSON>, Dict, Any


class AICompanionException(Exception):
    """Base exception for all AI Companion System errors."""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses."""
        return {
            "error": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details
        }


class ConfigurationError(AICompanionException):
    """Raised when there's a configuration error."""
    pass


class ServiceError(AICompanionException):
    """Raised when a service encounters an error."""
    pass


class ValidationError(AICompanionException):
    """Raised when input validation fails."""
    pass


class ResourceNotFoundError(AICompanionException):
    """Raised when a requested resource is not found."""
    pass


class ResourceExistsError(AICompanionException):
    """Raised when trying to create a resource that already exists."""
    pass


class PermissionError(AICompanionException):
    """Raised when user lacks permission for an operation."""
    pass


class QuotaExceededError(AICompanionException):
    """Raised when a quota or limit is exceeded."""
    pass


class TimeoutError(AICompanionException):
    """Raised when an operation times out."""
    pass


class DataCorruptionError(AICompanionException):
    """Raised when data corruption is detected."""
    pass


class SecurityError(AICompanionException):
    """Raised when a security violation is detected."""
    pass
