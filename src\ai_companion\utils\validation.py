"""
Input validation and sanitization utilities for the AI Companion System.
"""

import re
import html
import logging
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, validator

from ..exceptions import ValidationError


class MessageValidator(BaseModel):
    """Validator for user messages."""
    
    content: str = Field(..., min_length=1, max_length=5000)
    user_id: str = Field(..., min_length=1, max_length=100)
    context: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    @validator('content')
    def validate_content(cls, v):
        """Validate and sanitize message content."""
        if not v or not v.strip():
            raise ValueError("Message content cannot be empty")
        
        # Remove excessive whitespace
        v = re.sub(r'\s+', ' ', v.strip())
        
        # HTML escape for security
        v = html.escape(v)
        
        # Check for potential injection attempts
        suspicious_patterns = [
            r'<script[^>]*>.*?</script>',
            r'javascript:',
            r'data:text/html',
            r'vbscript:',
            r'onload\s*=',
            r'onerror\s*=',
        ]
        
        for pattern in suspicious_patterns:
            if re.search(pattern, v, re.IGNORECASE):
                raise ValueError("Message contains potentially malicious content")
        
        return v
    
    @validator('user_id')
    def validate_user_id(cls, v):
        """Validate user ID format."""
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError("User ID contains invalid characters")
        return v


class ConversationContextValidator(BaseModel):
    """Validator for conversation context."""
    
    session_id: Optional[str] = Field(None, max_length=100)
    interface: Optional[str] = Field(None, regex=r'^(gradio|api|cli|whatsapp)$')
    timestamp: Optional[str] = Field(None)
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    @validator('session_id')
    def validate_session_id(cls, v):
        """Validate session ID format."""
        if v and not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError("Session ID contains invalid characters")
        return v


def sanitize_text(text: str, max_length: int = 5000) -> str:
    """Sanitize text input for safe processing."""
    if not text:
        return ""
    
    # Truncate if too long
    if len(text) > max_length:
        text = text[:max_length]
    
    # Remove control characters except newlines and tabs
    text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    
    # Normalize whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # HTML escape
    text = html.escape(text)
    
    return text


def validate_api_key(api_key: str) -> bool:
    """Validate API key format."""
    if not api_key:
        return False
    
    # Basic format validation
    if len(api_key) < 20 or len(api_key) > 200:
        return False
    
    # Check for valid characters
    if not re.match(r'^[a-zA-Z0-9_-]+$', api_key):
        return False
    
    return True


def validate_user_input(
    message: str, 
    user_id: str, 
    context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Comprehensive user input validation."""
    try:
        # Validate using Pydantic model
        validator = MessageValidator(
            content=message,
            user_id=user_id,
            context=context or {}
        )
        
        return {
            "valid": True,
            "sanitized_message": validator.content,
            "user_id": validator.user_id,
            "context": validator.context
        }
        
    except ValueError as e:
        logging.warning(f"Input validation failed: {e}")
        raise ValidationError(f"Invalid input: {e}")


def check_rate_limit_key(key: str) -> bool:
    """Validate rate limit key format."""
    return bool(re.match(r'^[a-zA-Z0-9_:-]+$', key))


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file operations."""
    # Remove path traversal attempts
    filename = filename.replace('..', '').replace('/', '').replace('\\', '')
    
    # Keep only alphanumeric, dots, dashes, and underscores
    filename = re.sub(r'[^a-zA-Z0-9._-]', '', filename)
    
    # Limit length
    if len(filename) > 255:
        filename = filename[:255]
    
    return filename


def validate_memory_content(content: str) -> str:
    """Validate and sanitize memory content."""
    if not content or len(content.strip()) == 0:
        raise ValidationError("Memory content cannot be empty")
    
    if len(content) > 10000:  # 10KB limit
        raise ValidationError("Memory content too large")
    
    return sanitize_text(content, max_length=10000)


def validate_emotion_data(emotion_data: Dict[str, Any]) -> Dict[str, Any]:
    """Validate emotion analysis data."""
    required_fields = ['primary_emotion', 'intensity', 'confidence']
    
    for field in required_fields:
        if field not in emotion_data:
            raise ValidationError(f"Missing required emotion field: {field}")
    
    # Validate intensity and confidence ranges
    for field in ['intensity', 'confidence']:
        value = emotion_data[field]
        if not isinstance(value, (int, float)) or not 0 <= value <= 1:
            raise ValidationError(f"{field} must be a number between 0 and 1")
    
    return emotion_data
