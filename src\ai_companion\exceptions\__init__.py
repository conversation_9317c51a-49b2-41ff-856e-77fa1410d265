"""
Custom exceptions for the AI Companion System.
"""

from .base import (
    AICompanionException,
    ConfigurationError,
    ServiceError,
    ValidationError
)

# Import Gemini-specific exceptions
from .gemini import (
    GeminiServiceError,
    GeminiQuotaExceededError,
    GeminiAuthenticationError,
    GeminiTimeoutError,
    GeminiContentBlockedError
)

# API exceptions will be added when api.py is created
# from .api import (
#     APIError,
#     RateLimitError,
#     AuthenticationError,
#     AuthorizationError
# )

from .services import (
    GeminiServiceError,
    StorageServiceError,
    MemoryServiceError,
    EmotionalIntelligenceError,
    CrisisDetectionError
)

__all__ = [
    # Base exceptions
    "AICompanionException",
    "ConfigurationError",
    "ServiceError",
    "ValidationError",

    # Service exceptions
    "GeminiServiceError",
    "StorageServiceError",
    "MemoryServiceError",
    "EmotionalIntelligenceError",
    "CrisisDetectionError"
]
