"""
Service-specific exceptions for the AI Companion System.
"""

from typing import Optional, Dict, Any
from .base import ServiceError


class GeminiServiceError(ServiceError):
    """Raised when Gemini API encounters an error."""
    
    def __init__(
        self, 
        message: str, 
        status_code: Optional[int] = None,
        quota_exceeded: bool = False,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.status_code = status_code
        self.quota_exceeded = quota_exceeded


class StorageServiceError(ServiceError):
    """Raised when storage operations fail."""
    
    def __init__(
        self, 
        message: str, 
        operation: Optional[str] = None,
        table: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.operation = operation
        self.table = table


class MemoryServiceError(ServiceError):
    """Raised when memory operations fail."""
    
    def __init__(
        self, 
        message: str, 
        memory_type: Optional[str] = None,
        user_id: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.memory_type = memory_type
        self.user_id = user_id


class EmotionalIntelligenceError(ServiceError):
    """Raised when emotional intelligence analysis fails."""
    
    def __init__(
        self, 
        message: str, 
        analysis_type: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.analysis_type = analysis_type


class CrisisDetectionError(ServiceError):
    """Raised when crisis detection fails."""
    
    def __init__(
        self, 
        message: str, 
        risk_level: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.risk_level = risk_level


class ConversationServiceError(ServiceError):
    """Raised when conversation processing fails."""
    
    def __init__(
        self, 
        message: str, 
        conversation_id: Optional[str] = None,
        user_id: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.conversation_id = conversation_id
        self.user_id = user_id


class WhatsAppServiceError(ServiceError):
    """Raised when WhatsApp integration fails."""
    
    def __init__(
        self, 
        message: str, 
        webhook_error: bool = False,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.webhook_error = webhook_error
